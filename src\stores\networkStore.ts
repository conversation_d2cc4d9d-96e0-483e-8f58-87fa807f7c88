import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { localModelService, LocalModel } from '../services/localModelService'

interface NetworkState {
  isOnline: boolean
  isPrivateMode: boolean
  localModelsAvailable: boolean
  ollamaConnected: boolean
  lmStudioConnected: boolean
  localModels: LocalModel[]

  // Actions
  toggleOnline: () => void
  togglePrivateMode: () => void
  setLocalModelsAvailable: (available: boolean) => void
  setOllamaConnected: (connected: boolean) => void
  setLmStudioConnected: (connected: boolean) => void
  setLocalModels: (models: LocalModel[]) => void
  checkLocalModels: () => Promise<void>
  getAvailableModels: () => LocalModel[]
  autoSelectLocalModel: () => void
}

export const useNetworkStore = create<NetworkState>()(
  persist(
    (set, get) => ({
      // Initial state
      isOnline: true,
      isPrivateMode: false,
      localModelsAvailable: false,
      ollamaConnected: false,
      lmStudioConnected: false,
      localModels: [],

      // Toggle network connectivity
      toggleOnline: () => {
        const currentState = get()
        const newOnlineState = !currentState.isOnline
        
        set({ isOnline: newOnlineState })
        
        // If going offline, enable private mode
        if (!newOnlineState) {
          set({ isPrivateMode: true })
        }
      },

      // Toggle private mode
      togglePrivateMode: () => {
        const currentState = get()
        const newPrivateMode = !currentState.isPrivateMode

        set({ isPrivateMode: newPrivateMode })

        // If enabling private mode, force offline and check local models
        if (newPrivateMode) {
          set({ isOnline: false })
          // Check local models when private mode is enabled
          get().checkLocalModels().then(() => {
            // Auto-select first available local model if none is selected
            get().autoSelectLocalModel()
          })
        }
      },

      // Set local models availability
      setLocalModelsAvailable: (available: boolean) => {
        set({ localModelsAvailable: available })
      },

      // Set local models list
      setLocalModels: (models: LocalModel[]) => {
        set({ localModels: models })
      },

      // Set Ollama connection status
      setOllamaConnected: (connected: boolean) => {
        set({ ollamaConnected: connected })
        // Update overall local models availability
        const { lmStudioConnected } = get()
        set({ localModelsAvailable: connected || lmStudioConnected })
      },

      // Set LM Studio connection status
      setLmStudioConnected: (connected: boolean) => {
        set({ lmStudioConnected: connected })
        // Update overall local models availability
        const { ollamaConnected } = get()
        set({ localModelsAvailable: connected || ollamaConnected })
      },

      // Check for local model providers
      checkLocalModels: async () => {
        const providerStatus = await localModelService.getProviderStatus()
        const allModels = await localModelService.getAllLocalModels()

        get().setOllamaConnected(providerStatus.ollama.isConnected)
        get().setLmStudioConnected(providerStatus.lmstudio.isConnected)
        get().setLocalModels(allModels)
        get().setLocalModelsAvailable(allModels.length > 0)

        // Auto-select local model if in private mode and models are available
        if (get().isPrivateMode && allModels.length > 0) {
          get().autoSelectLocalModel()
        }
      },

      // Get available models based on current mode
      getAvailableModels: () => {
        const { isPrivateMode, localModels } = get()
        if (isPrivateMode) {
          return localModels
        }
        // In non-private mode, return all models (local + external)
        // External models would be added here from other sources
        return localModels
      },

      // Auto-select a local model when private mode is enabled
      autoSelectLocalModel: () => {
        const { localModels, isPrivateMode } = get()

        if (!isPrivateMode || localModels.length === 0) {
          return
        }

        // Import app store to update selected model
        import('../store').then(({ useAppStore }) => {
          const appStore = useAppStore.getState()
          const currentSelectedModel = appStore.settings.selectedModel

          // Only auto-select if no model is currently selected or current model is not a local model
          const isCurrentModelLocal = currentSelectedModel && currentSelectedModel.includes(':')

          if (!currentSelectedModel || !isCurrentModelLocal) {
            // Select the first available local model (they're already sorted by provider)
            const defaultModel = localModels[0]
            if (defaultModel) {
              appStore.updateSettings({ selectedModel: defaultModel.id })

              // Save to electron store if available
              if (window.electronAPI?.settings) {
                window.electronAPI.settings.set('app-settings', {
                  ...appStore.settings,
                  selectedModel: defaultModel.id
                })
              }
            }
          }
        })
      }
    }),
    {
      name: 'network-storage',
      partialize: (state) => ({
        isOnline: state.isOnline,
        isPrivateMode: state.isPrivateMode
      })
    }
  )
)
